const { allQueues } = require('../queues_v2/queues');
const HttpStatus = require('http-status-codes');
const sampleOperationResp = require('../utils/operationResp');
const {
    transformCapacityRecord,
    generateTestCapacityRecords,
    callLambdaWithBatchData,
    parseLambdaResponse,
    evaluateResponseSuccess,
    createSuccessResponse,
    createErrorResponse,
} = require('./utils');

class CapacityUpdateModel {
    // Static flag to control processing mode (stream or direct)
    static USE_DIRECT_QUERY = false; // Set to true for direct query, false for streaming

    constructor() {
        this.db = null;
        this.databaseReplica = null;
        this.ip_address = null;
        this.user_agent_ = null;
    }

    /**
     * Initiates the capacity update process
     * @param {Object} query - Query parameters from the request
     * @returns {Promise} - Promise that resolves to a response object
     */
    initiateCapacityUpdate(query) {
        return new Promise(async (resolve) => {
            try {
                if (!this.db) {
                    console.error('Database connection not available');
                    resolve({
                        status: false,
                        message: 'Database connection not available',
                        timestamp: new Date().toISOString(),
                    });
                    return;
                }
                const dbResp = (
                    await this.db.tms_ace_get_capacity_enabled_orgs()
                )[0].tms_ace_get_capacity_enabled_orgs;

                // Check if capacity module is enabled for any organizations
                if (
                    !dbResp.status ||
                    !dbResp.data ||
                    !dbResp.data.enabled_organizations ||
                    !dbResp.data.enabled_organizations.length
                ) {
                    return resolve(
                        new sampleOperationResp(
                            false,
                            {
                                status: 'error',
                                message:
                                    'No organizations with capacity module enabled found',
                                timestamp: new Date().toISOString(),
                            },
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                }

                const enabledOrgs = dbResp.data.enabled_organizations;

                // Track jobs added
                const jobsAdded = [];

                // Loop through each organization and add a job to the queue
                for (const org of enabledOrgs) {
                    // Prepare job data for the queue
                    const jobData = {
                        query: query,
                        org_id: org.org_id,
                        org_name: org.org_name,
                        ip_address: this.ip_address,
                        user_agent: this.user_agent_,
                        timestamp: new Date().toISOString(),
                    };

                    // Add job to the queue
                    // Using our dedicated CRON_CAPACITY_UPDATE queue
                    allQueues.CRON_CAPACITY_UPDATE.addJob(jobData);

                    jobsAdded.push({
                        org_id: org.org_id,
                        org_name: org.org_name,
                    });
                }

                // Return success response
                resolve(
                    new sampleOperationResp(
                        true,
                        {
                            status: 'success',
                            message: `Capacity sync triggered for ${jobsAdded.length} organizations`,
                            jobs_added: jobsAdded,
                            timestamp: new Date().toISOString(),
                        },
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error('Error in initiateCapacityUpdate:', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        {
                            status: 'error',
                            message:
                                error.message ||
                                'Failed to initiate capacity update',
                            timestamp: new Date().toISOString(),
                        },
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    /**
     * Process the capacity update
     * @param {Object} data - The job data from the queue
     * @returns {Promise} - Promise that resolves to a result object
     */
    processCapacityUpdate(data) {
        return new Promise(async (resolve) => {
            try {
                if (!this.db) {
                    console.error('Database connection not available');
                    resolve({
                        status: false,
                        message: 'Database connection not available',
                        timestamp: new Date().toISOString(),
                    });
                    return;
                }

                // Extract organization ID from job data
                const orgId = data.org_id;
                if (!orgId) {
                    console.error('Organization ID not provided in job data');
                    resolve({
                        status: false,
                        message: 'Organization ID not provided in job data',
                        timestamp: new Date().toISOString(),
                    });
                    return;
                }

                // Step 1: Get unique resource ID combinations
                try {
                    console.log(
                        `ORG_ID: ${orgId} | FETCH: resource_combinations`
                    );

                    // Call the new database function to get unique resource combinations
                    const resourceCombinations = await this.db.query(
                        'SELECT * FROM public.tms_ace_get_unique_resource_combinations($1)',
                        [orgId]
                    );

                    console.log(
                        `FOUND: ${resourceCombinations.length} | TYPE: resource_combinations`
                    );

                    // If no resource combinations were found, return an error
                    if (resourceCombinations.length === 0) {
                        console.error('No resource combinations found');
                        resolve({
                            status: false,
                            message: 'No resource combinations found',
                            timestamp: new Date().toISOString(),
                        });
                        return;
                    }

                    // Step 2: Create a background job for each resource combination
                    const jobsAdded = [];
                    let successCount = 0;
                    let failureCount = 0;

                    for (const resource of resourceCombinations) {
                        try {
                            // Prepare job data for the queue
                            const jobData = {
                                orgId: orgId,
                                orgName: data.org_name,
                                resourceId: resource.resource_id,
                                providerId: resource.provider_id,
                                verticalId: resource.vertical_id,
                                skillId: resource.skill_id,
                                hubId: resource.hub_id,
                                ip_address: this.ip_address,
                                user_agent: this.user_agent_,
                                timestamp: new Date().toISOString(),
                            };

                            // Add job to the dedicated resource update queue
                            allQueues.CRON_CAPACITY_RESOURCE_UPDATE.addJob(
                                jobData,
                                {
                                    attempts: 10, // Retry up to 10 times
                                    backoff: {
                                        type: 'exponential',
                                        delay: 5000, // Start with 5 seconds delay, then exponential backoff
                                    },
                                    removeOnComplete: true,
                                    removeOnFail: false, // Keep failed jobs for inspection
                                }
                            );

                            jobsAdded.push({
                                resourceId: resource.resource_id,
                                verticalName: resource.vertical_name,
                                skillName: resource.skill_name,
                                hubName: resource.hub_name,
                            });

                            successCount++;
                        } catch (queueError) {
                            console.error(
                                `Error adding resource job to queue for resource ID ${resource.resource_id}:`,
                                queueError
                            );
                            failureCount++;
                        }
                    }

                    // Return success with the job summary
                    resolve({
                        status: failureCount === 0,
                        message: `Capacity update jobs created for ${successCount} resource combinations`,
                        data: {
                            totalResources: resourceCombinations.length,
                            successCount,
                            failureCount,
                            sampleJobs: jobsAdded.slice(0, 5), // Include just a few sample jobs
                        },
                        timestamp: new Date().toISOString(),
                    });
                } catch (dbError) {
                    console.error(
                        'Error fetching resource combinations:',
                        dbError
                    );
                    resolve({
                        status: false,
                        message:
                            dbError.message ||
                            'Failed to fetch resource combinations',
                        timestamp: new Date().toISOString(),
                    });
                }
            } catch (error) {
                console.error('Error in processCapacityUpdate:', error);
                resolve({
                    status: false,
                    message:
                        error.message || 'Failed to process capacity update',
                    timestamp: new Date().toISOString(),
                });
            }
        });
    }

    /**
     * Process capacity data for a specific resource combination
     * @param {number} orgId - The organization ID
     * @param {string} orgName - The organization name
     * @param {string} resourceId - The resource ID
     * @param {number} providerId - The provider ID (not used directly but kept for consistency)
     * @param {number} verticalId - The vertical ID
     * @param {number} skillId - The skill ID
     * @param {number} hubId - The hub ID
     * @returns {Promise} - Promise that resolves to a result object
     */
    processResourceCapacityUpdate(
        orgId,
        orgName,
        resourceId,
        providerId, // Not used directly but kept for consistency with job data
        verticalId,
        skillId,
        hubId
    ) {
        return new Promise(async (resolve) => {
            try {
                if (!this.db) {
                    console.error('Database connection not available');
                    resolve({
                        status: false,
                        message: 'Database connection not available',
                        timestamp: new Date().toISOString(),
                    });
                    return;
                }

                console.log(
                    `${resourceId} | ORG_ID: ${orgId} | VERTICAL: ${verticalId} | SKILL: ${skillId} | HUB: ${hubId}`
                );

                // Fetch capacity data for this specific resource combination
                try {
                    const capacityData = await this.db.query(
                        'SELECT * FROM public.tms_ace_get_capacity_data_by_resource($1, $2, $3, $4)',
                        [orgId, verticalId, skillId, hubId]
                    );

                    console.log(
                        `${resourceId} | RECORDS: ${capacityData.length}`
                    );

                    // If no records were found, return an error
                    if (capacityData.length === 0) {
                        console.error(`${resourceId} | ERROR: no_records`);
                        resolve({
                            status: false,
                            message: `No capacity records found for resource ID: ${resourceId}`,
                            timestamp: new Date().toISOString(),
                        });
                        return;
                    }

                    // Process capacity data using database function instead of Lambda
                    const BATCH_SIZE = 5000; // Increased batch size for database operations
                    const totalBatches = Math.ceil(
                        capacityData.length / BATCH_SIZE
                    );
                    const results = [];
                    let successCount = 0;
                    let failureCount = 0;

                    console.log(
                        `${resourceId} | PROCESSING: ${capacityData.length} records in ${totalBatches} batches using database function`
                    );

                    // Process capacity data in batches using database function
                    for (
                        let batchIndex = 0;
                        batchIndex < totalBatches;
                        batchIndex++
                    ) {
                        const startIdx = batchIndex * BATCH_SIZE;
                        const endIdx = Math.min(
                            startIdx + BATCH_SIZE,
                            capacityData.length
                        );
                        const batchData = capacityData.slice(startIdx, endIdx);

                        console.log(
                            `${resourceId} | BATCH: ${batchIndex + 1}/${totalBatches} | RECORDS: ${batchData.length}`
                        );

                        try {
                            // Transform the batch data to match the expected format
                            const transformedBatch = batchData.map((record) =>
                                transformCapacityRecord(record, orgId, orgName)
                            );

                            // Call the database function to insert/update capacity data
                            const dbResponse = await this.db.query(
                                'SELECT * FROM public.tms_ace_bulk_upsert_capacity_data($1, $2, $3, $4)',
                                [
                                    JSON.stringify(transformedBatch),
                                    orgId,
                                    this.ip_address,
                                    this.user_agent_,
                                ]
                            );

                            // Parse the database response
                            const result =
                                dbResponse[0]
                                    ?.tms_ace_bulk_upsert_capacity_data;

                            if (result && result.status) {
                                results.push({
                                    batchIndex: batchIndex + 1,
                                    recordCount: batchData.length,
                                    success: true,
                                    message: result.message,
                                    data: result.data,
                                    statusCode: 200,
                                });
                                successCount +=
                                    result.data.success_count ||
                                    batchData.length;
                                failureCount += result.data.failure_count || 0;
                            } else {
                                results.push({
                                    batchIndex: batchIndex + 1,
                                    recordCount: batchData.length,
                                    success: false,
                                    message:
                                        result?.message ||
                                        'Database operation failed',
                                    statusCode: 500,
                                });
                                failureCount += batchData.length;
                            }
                        } catch (error) {
                            console.error(
                                `CapacityUpdateModel::processResourceCapacityUpdate:: Error processing batch ${batchIndex + 1} for resource ID ${resourceId}:`,
                                error
                            );
                            results.push({
                                batchIndex: batchIndex + 1,
                                recordCount: batchData.length,
                                success: false,
                                message: `Error processing batch: ${error.message || 'Unknown error'}`,
                                statusCode: error.statusCode || 500,
                            });
                            failureCount += batchData.length;
                        }
                    }

                    // Return the results
                    const isFullySuccessful = failureCount === 0;
                    resolve({
                        status: isFullySuccessful,
                        message: isFullySuccessful
                            ? `Capacity update for resource ID ${resourceId} completed successfully using database operations`
                            : `Capacity update for resource ID ${resourceId} completed with ${failureCount} failures out of ${capacityData.length} records using database operations`,
                        data: {
                            resourceId,
                            totalRecords: capacityData.length,
                            successCount,
                            failureCount,
                            processingMethod: 'database_function',
                            sampleResults: results.slice(0, 3), // Include just a few sample results
                        },
                        timestamp: new Date().toISOString(),
                    });
                } catch (dbError) {
                    console.error(
                        `CapacityUpdateModel::processResourceCapacityUpdate:: Error fetching capacity data for resource ID ${resourceId}:`,
                        dbError
                    );
                    resolve({
                        status: false,
                        message:
                            dbError.message ||
                            `Failed to fetch capacity data for resource ID ${resourceId}`,
                        timestamp: new Date().toISOString(),
                    });
                }
            } catch (error) {
                console.error(
                    `CapacityUpdateModel::processResourceCapacityUpdate:: Error in processResourceCapacityUpdate for resource ID ${resourceId}:`,
                    error
                );
                resolve({
                    status: false,
                    message:
                        error.message ||
                        `Failed to process capacity update for resource ID ${resourceId}`,
                    timestamp: new Date().toISOString(),
                });
            }
        });
    }

    // Getter and setter methods
    set ip_addr(ip_addr_) {
        this.ip_address = ip_addr_;
    }

    get ip_addr() {
        return this.ip_address;
    }

    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    get user_agent() {
        return this.user_agent_;
    }

    // transformCapacityRecord method has been moved to utils.js

    // _callLambdaWithBatchData method has been moved to utils.js as callLambdaWithBatchData

    // _parseLambdaResponse method has been moved to utils.js as parseLambdaResponse

    // _evaluateResponseSuccess method has been moved to utils.js as evaluateResponseSuccess

    // _createSuccessResponse method has been moved to utils.js as createSuccessResponse

    // _createErrorResponse method has been moved to utils.js as createErrorResponse

    // Method to get a fresh instance of the model
    getFreshInstance(model) {
        const clonedInstance = new CapacityUpdateModel();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports = new CapacityUpdateModel();
